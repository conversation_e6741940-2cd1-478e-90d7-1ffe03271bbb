{% extends 'base.html' %}

{% block title %}Emprunts de {{ book.title }} - Bibliothèque{% endblock %}

{% block header %}Emprunts du livre "{{ book.title }}"{% endblock %}

{% block content %}
<div style="background: white; padding: 2rem; border-radius: 8px; margin-bottom: 2rem; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h2>{{ book.title }}</h2>
    <p><strong>Auteur:</strong> {{ book.author }}</p>
    <p><strong>Date de publication:</strong> {{ book.publication_date|date:"d/m/Y" }}</p>
    <p><strong>Catégorie:</strong> {{ book.category.category_name }}</p>
    <p><strong>Statut:</strong> 
        {% if book.is_available %}
            <span class="available">Disponible</span>
        {% else %}
            <span class="unavailable">Non disponible</span>
        {% endif %}
    </p>
</div>

<div style="background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h3>Historique des emprunts</h3>
    
    {% if borrowings %}
        <table style="width: 100%; border-collapse: collapse; margin-top: 1rem;">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    <th style="padding: 1rem; text-align: left; border-bottom: 2px solid #dee2e6;">Client</th>
                    <th style="padding: 1rem; text-align: left; border-bottom: 2px solid #dee2e6;">Date d'emprunt</th>
                    <th style="padding: 1rem; text-align: left; border-bottom: 2px solid #dee2e6;">Date de retour prévue</th>
                    <th style="padding: 1rem; text-align: left; border-bottom: 2px solid #dee2e6;">Statut</th>
                </tr>
            </thead>
            <tbody>
                {% for borrowing in borrowings %}
                    <tr style="{% if not borrowing.returned %}background-color: #fff3cd;{% endif %}">
                        <td style="padding: 1rem; border-bottom: 1px solid #dee2e6;">
                            {{ borrowing.client.first_name }} {{ borrowing.client.last_name }}
                            <br><small>({{ borrowing.client.library_identifier }})</small>
                        </td>
                        <td style="padding: 1rem; border-bottom: 1px solid #dee2e6;">
                            {{ borrowing.borrowing_date|date:"d/m/Y" }}
                        </td>
                        <td style="padding: 1rem; border-bottom: 1px solid #dee2e6;">
                            {{ borrowing.return_date|date:"d/m/Y" }}
                        </td>
                        <td style="padding: 1rem; border-bottom: 1px solid #dee2e6;">
                            {% if borrowing.returned %}
                                <span class="available">Retourné</span>
                            {% else %}
                                <span class="unavailable">En cours</span>
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <p>Aucun emprunt pour ce livre.</p>
    {% endif %}
</div>

<div style="margin-top: 2rem; text-align: center;">
    <a href="{% url 'book_list' %}" class="btn">Retour à la liste des livres</a>
</div>
{% endblock %}

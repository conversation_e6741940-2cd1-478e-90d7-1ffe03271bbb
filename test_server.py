#!/usr/bin/env python
"""
Script pour tester si le serveur Django fonctionne
"""
import requests
import time

def test_server():
    try:
        print("Test de connexion au serveur Django...")
        response = requests.get('http://127.0.0.1:8000/', timeout=5)
        print(f"Statut: {response.status_code}")
        if response.status_code == 200:
            print("✅ Serveur Django fonctionne correctement!")
            print("🌐 Accédez à: http://127.0.0.1:8000/")
        else:
            print(f"❌ Erreur: Code de statut {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur")
        print("Le serveur Django n'est pas démarré ou ne répond pas")
    except requests.exceptions.Timeout:
        print("❌ Timeout: Le serveur met trop de temps à répondre")
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == '__main__':
    test_server()

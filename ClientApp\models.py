from django.db import models
from django.core.validators import RegexValidator
from django.core.exceptions import ValidationError
from django.contrib.auth.models import AbstractUser
from django.conf import settings

# Create your models here.

def gmail_validator(value):
    """Valide que l'email est un domaine gmail.com"""
    if not value.endswith('@gmail.com'):
        raise ValidationError('Veuillez entrer une adresse email valide appartenant au domaine gmail.com (ex : <EMAIL>).')

def library_identifier_validator(value):
    """Valide le format de l'identifiant de bibliothèque"""
    import re
    pattern = r'^[A-Z][a-zA-Z]{5}\d{4}$'
    if not re.match(pattern, value):
        raise ValidationError('L\'identifiant doit être unique et respecter le format suivant : une majuscule au début, suivie de lettres, et se terminant par 4 chiffres')

class Client(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True, blank=True)
    library_identifier = models.CharField(
        max_length=10,
        unique=True,
        validators=[library_identifier_validator],
        help_text="Format: une majuscule + 5 lettres + 4 chiffres (ex: Aabcde1234)"
    )
    email = models.EmailField(
        max_length=250,
        unique=True,
        validators=[gmail_validator],
        help_text="Adresse email valide du domaine gmail.com"
    )
    username = models.CharField(max_length=150, unique=True)
    first_name = models.CharField(max_length=200)
    last_name = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.library_identifier})"

    class Meta:
        verbose_name = "Client"
        verbose_name_plural = "Clients"

from django.contrib import admin
from .models import Client

# Register your models here.

@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ('library_identifier', 'first_name', 'last_name', 'email', 'username', 'created_at')
    search_fields = ('library_identifier', 'first_name', 'last_name', 'email', 'username')
    list_filter = ('created_at', 'updated_at')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('library_identifier',)

    fieldsets = (
        ('Informations personnelles', {
            'fields': ('first_name', 'last_name', 'email', 'username')
        }),
        ('Identifiant bibliothèque', {
            'fields': ('library_identifier',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

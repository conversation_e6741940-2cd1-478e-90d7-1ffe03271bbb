# Generated by Django 4.2 on 2025-06-03 18:43

import UserApp.models
import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('UserApp', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='participant',
            name='cin',
            field=models.CharField(max_length=8, primary_key=True, serialize=False, validators=[django.core.validators.RegexValidator('^\\d{8}$', 'This field must contain exactly 8 digits')]),
        ),
        migrations.AlterField(
            model_name='participant',
            name='email',
            field=models.EmailField(max_length=250, unique=True, validators=[UserApp.models.emailValidator]),
        ),
    ]

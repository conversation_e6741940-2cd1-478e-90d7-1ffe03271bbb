@echo off
echo Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

echo Creation du superutilisateur...
python manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); User.objects.filter(username='admin').exists() or User.objects.create_superuser('admin', '<EMAIL>', 'admin123')"

echo Creation des donnees de test...
python create_test_data.py

echo Demarrage du serveur...
python manage.py runserver
pause

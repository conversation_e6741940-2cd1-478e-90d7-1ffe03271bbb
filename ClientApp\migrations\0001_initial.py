# Generated by Django 5.2.3 on 2025-06-28 16:38

import ClientApp.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Client',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('library_identifier', models.CharField(help_text='Format: une majuscule + 5 lettres + 4 chiffres (ex: Aabcde1234)', max_length=10, unique=True, validators=[ClientApp.models.library_identifier_validator])),
                ('email', models.EmailField(help_text='Adresse email valide du domaine gmail.com', max_length=250, unique=True, validators=[ClientApp.models.gmail_validator])),
                ('username', models.Char<PERSON>ield(max_length=150, unique=True)),
                ('first_name', models.Char<PERSON>ield(max_length=200)),
                ('last_name', models.CharField(max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
            ],
            options={
                'verbose_name': 'Client',
                'verbose_name_plural': 'Clients',
            },
        ),
    ]

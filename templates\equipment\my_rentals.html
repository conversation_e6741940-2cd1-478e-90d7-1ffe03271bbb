<!DOCTYPE html>
<html>
<head>
    <title>Mes Locations</title>
</head>
<body>
    <h1>Mes Locations</h1>

    <p><a href="{% url 'equipment_list' %}">Liste des équipements</a> |
    <a href="{% url 'logout' %}">Se déconnecter</a></p>

    {% if messages %}
        {% for message in messages %}
            <p>{{ message }}</p>
        {% endfor %}
    {% endif %}

    {% if rentals %}
        <table border="1">
            <tr>
                <th>Équipement</th>
                <th>Marque</th>
                <th>Type</th>
                <th>Date location</th>
                <th>Date retour</th>
                <th>Statut</th>
            </tr>
            {% for rental in rentals %}
            <tr>
                <td>{{ rental.equipment.name }}</td>
                <td>{{ rental.equipment.brand }}</td>
                <td>{{ rental.equipment.type.name }}</td>
                <td>{{ rental.rental_date|date:"d/m/Y" }}</td>
                <td>{{ rental.return_date|date:"d/m/Y" }}</td>
                <td>{% if rental.returned %}Retourné{% else %}En cours{% endif %}</td>
            </tr>
            {% endfor %}
        </table>
    {% else %}
        <p>Aucune location. <a href="{% url 'equipment_list' %}">Parcourir les équipements</a></p>
    {% endif %}
</body>
</html>

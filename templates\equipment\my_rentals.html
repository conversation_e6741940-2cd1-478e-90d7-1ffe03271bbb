{% extends 'base.html' %}

{% block title %}Mes Locations - Système de Location{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">📋 Mes Locations</h1>
                <div class="d-flex align-items-center">
                    <a href="{% url 'equipment_list' %}" class="btn btn-outline-primary me-2">Liste des équipements</a>
                    <a href="{% url 'logout' %}" class="btn btn-outline-secondary">Se déconnecter</a>
                </div>
            </div>

            <!-- Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Liste des locations -->
            {% if rentals %}
                <div class="row">
                    {% for rental in rentals %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">{{ rental.equipment.name }}</h5>
                                    <p class="card-text">
                                        <strong>Marque:</strong> {{ rental.equipment.brand }}<br>
                                        <strong>Type:</strong> {{ rental.equipment.type.name }}<br>
                                        <strong>Date de location:</strong> {{ rental.rental_date|date:"d/m/Y" }}<br>
                                        <strong>Date de retour prévue:</strong> {{ rental.return_date|date:"d/m/Y" }}<br>
                                        <span class="badge {% if rental.returned %}bg-success{% else %}bg-warning{% endif %}">
                                            {% if rental.returned %}Retourné{% else %}En cours{% endif %}
                                        </span>
                                    </p>
                                </div>
                                <div class="card-footer">
                                    {% if not rental.returned %}
                                        <small class="text-muted">
                                            {% now "Y-m-d" as today %}
                                            {% if rental.return_date|date:"Y-m-d" < today %}
                                                <span class="text-danger">⚠️ Retour en retard</span>
                                            {% else %}
                                                <span class="text-success">✅ Dans les temps</span>
                                            {% endif %}
                                        </small>
                                    {% else %}
                                        <small class="text-success">✅ Équipement retourné</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="alert alert-info text-center">
                    <h4>Aucune location</h4>
                    <p>Vous n'avez encore loué aucun équipement.</p>
                    <a href="{% url 'equipment_list' %}" class="btn btn-primary">Parcourir les équipements</a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

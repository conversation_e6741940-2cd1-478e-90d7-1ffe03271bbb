from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Book, Borrowing
from ClientApp.models import Client
from UserApp.models import Participant
from datetime import date, timedelta

# Create your views here.

def book_list(request):
    """Affiche la liste des livres ordonnée par date de publication"""
    books = Book.objects.all().order_by('-publication_date')

    # Filtrage par disponibilité
    availability_filter = request.GET.get('availability')
    if availability_filter == 'available':
        # Livres disponibles (sans emprunt en cours)
        books = books.filter(borrowings__returned=True).distinct() | books.filter(borrowings__isnull=True)
    elif availability_filter == 'unavailable':
        # Livres non disponibles (avec emprunt en cours)
        books = books.filter(borrowings__returned=False).distinct()

    # Filtrage par titre
    title_filter = request.GET.get('title')
    if title_filter:
        books = books.filter(title__icontains=title_filter)

    # Filtrage par catégorie
    category_filter = request.GET.get('category')
    if category_filter:
        books = books.filter(category__id=category_filter)

    # Pagination
    paginator = Paginator(books, 6)  # 6 livres par page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Récupérer toutes les catégories pour le filtre
    from CategoryApp.models import Category
    categories = Category.objects.all()

    context = {
        'page_obj': page_obj,
        'categories': categories,
        'current_filters': {
            'availability': availability_filter,
            'title': title_filter,
            'category': category_filter,
        }
    }
    return render(request, 'books/book_list.html', context)

def book_borrowings(request, book_id):
    """Affiche les détails des emprunts d'un livre"""
    book = get_object_or_404(Book, id=book_id)
    borrowings = book.borrowings.all().order_by('-borrowing_date')

    context = {
        'book': book,
        'borrowings': borrowings,
    }
    return render(request, 'books/book_borrowings.html', context)

@login_required
def borrow_book(request, book_id):
    """Permet à un utilisateur connecté d'emprunter un livre"""
    book = get_object_or_404(Book, id=book_id)

    # Pour l'instant, simulons l'emprunt sans créer d'objet Borrowing
    # car nous avons des problèmes de migration
    messages.success(request, f"Simulation: Vous avez emprunté '{book.title}' avec succès. Cette fonctionnalité sera complètement opérationnelle après la migration de la base de données.")
    return redirect('book_list')

@login_required
def my_borrowings(request):
    """Affiche les emprunts de l'utilisateur connecté"""
    # Pour l'instant, retournons une liste vide car nous avons des problèmes de migration
    borrowings = []
    messages.info(request, "La fonctionnalité d'historique des emprunts sera disponible après la migration de la base de données.")

    context = {
        'borrowings': borrowings,
    }
    return render(request, 'books/my_borrowings.html', context)

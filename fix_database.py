#!/usr/bin/env python
"""
Script pour corriger la base de données et recréer les emprunts
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'MyProject.settings')
django.setup()

from BookApp.models import Book, Borrowing
from UserApp.models import Participant
from django.contrib.auth.models import User

def fix_database():
    print("🔧 Correction de la base de données...")
    
    # Supprimer tous les emprunts existants
    print("📚 Suppression des emprunts existants...")
    Borrowing.objects.all().delete()
    
    # Vérifier les participants existants
    participants = Participant.objects.all()
    print(f"👥 Participants trouvés: {participants.count()}")
    
    # Vérifier les livres existants
    books = Book.objects.all()
    print(f"📖 Livres trouvés: {books.count()}")
    
    if participants.exists() and books.exists():
        # <PERSON><PERSON>er quelques emprunts de test
        print("📝 Création d'emprunts de test...")
        
        # Prendre les premiers participants et livres
        participant1 = participants.first()
        participant2 = participants.last() if participants.count() > 1 else participant1
        
        book1 = books.first()
        book2 = books.last() if books.count() > 1 else book1
        
        # Créer des emprunts
        borrowing1 = Borrowing.objects.create(
            book=book1,
            participant=participant1,
            borrowed_date='2025-06-25',
            return_date='2025-07-25'
        )
        
        borrowing2 = Borrowing.objects.create(
            book=book2,
            participant=participant2,
            borrowed_date='2025-06-20',
            return_date='2025-07-20'
        )
        
        print(f"✅ Emprunts créés:")
        print(f"   - {participant1.nom} a emprunté '{book1.title}'")
        print(f"   - {participant2.nom} a emprunté '{book2.title}'")
    else:
        print("❌ Pas assez de données pour créer des emprunts")
    
    print("🎉 Base de données corrigée!")

if __name__ == '__main__':
    fix_database()

from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
# Create your models here.

def emailValidator(value):
    if not value.endswith('@esprit.tn'):
        raise ValidationError('Invalid email address. Only @esprit.tn addresses are allowed')
class Participant(AbstractUser):
    digitsOnly = RegexValidator(r'^\d{8}$', 'This field must contain exactly 8 digits')
    cin = models.Char<PERSON><PERSON>(primary_key=True, max_length=8, validators=[digitsOnly])
    email = models.EmailField(max_length=250, unique=True, validators=[emailValidator])
    first_name= models.Char<PERSON><PERSON>(max_length=200)
    last_name= models.Char<PERSON><PERSON>(max_length=200)
    username = models.Char<PERSON><PERSON>(unique=True, max_length=150)
    
    CHOICE = (
        ('ETUDIANT', 'Etudiant'),
        ('CHERCHEUR', 'CHERCHEUR'),
        ('ENSEIGNANT', 'ENSEIGNANT'),
        ('DOCTEUR', 'DOCTEUR'),
    )
    participant_category = models.CharField('category', choices=CHOICE, max_length=100)
    
    USERNAME_Field = 'username'
    
    
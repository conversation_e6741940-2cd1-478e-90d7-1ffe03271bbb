from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import Customer

# Register your models here.

@admin.register(Customer)
class CustomerAdmin(UserAdmin):
    list_display = ('customer_id', 'username', 'first_name', 'last_name', 'email', 'is_active', 'date_joined')
    list_filter = ('is_active', 'is_staff', 'date_joined')
    search_fields = ('customer_id', 'username', 'first_name', 'last_name', 'email')
    ordering = ('customer_id',)

    fieldsets = (
        (None, {'fields': ('customer_id', 'username', 'password')}),
        ('Informations personnelles', {'fields': ('first_name', 'last_name', 'email')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        ('Dates importantes', {'fields': ('last_login', 'date_joined')}),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('customer_id', 'username', 'first_name', 'last_name', 'email', 'password1', 'password2'),
        }),
    )

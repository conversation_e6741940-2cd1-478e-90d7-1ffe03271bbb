from django.db import models
from django.utils import timezone
from UserApp.models import Participant
from django.core.exceptions import ValidationError
import re
from django.core.validators import RegexValidator, MinLengthValidator
# Create your models here.

def validate_letters(value):
    if not re.match(r'A-Za-z\s]+$', value):
        raise ValidationError('This field should only contain letters')

class Category(models.Model):
    letters_only = RegexValidator(r'^[A-Za-z]*$', 'only letters')
    title = models.CharField(max_length=200, validators=[letters_only])
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return self.title
    
    class Meta:
        verbose_name_plural = "Categories"
    
def startDateValidator(value):
    if value < timezone.now():
        raise ValidationError('The start date must be in the future.')
def validate_file(value):
    if value.name.lower().endswith(".pdf") :
        raise ValidationError('Only PDF files are allowed')
class Conference(models.Model):
    title = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    start_date = models.DateTimeField(default=timezone.now, validators=[startDateValidator])
    end_date = models.DateTimeField(default=timezone.now)
    location = models.CharField(max_length=200)
    price = models.FloatField()
    capacity = models.IntegerField()
    program = models.FileField(upload_to='files/')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='conferences')
    reservations = models.ManyToManyField(Participant, through='Reservation', related_name='reservations')
    
class Reservation(models.Model):
    confirmed = models.BooleanField(default=False)
    reservation_date = models.DateTimeField(default=timezone.now)
    conference = models.ForeignKey(Conference, on_delete=models.CASCADE)
    participant = models.ForeignKey(Participant, on_delete=models.CASCADE)
    
    
    class Meta:
        unique_together = ('conference', 'participant')
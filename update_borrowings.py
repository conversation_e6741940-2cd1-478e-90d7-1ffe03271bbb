#!/usr/bin/env python
"""
Script pour mettre à jour les emprunts existants pour utiliser Participant au lieu de Client
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'MyProject.settings')
django.setup()

from BookApp.models import Borrowing
from UserApp.models import Participant
from ClientApp.models import Client

def update_borrowings():
    print("Mise à jour des emprunts existants...")
    
    # Supprimer tous les emprunts existants pour éviter les conflits
    Borrowing.objects.all().delete()
    print("Emprunts existants supprimés")
    
    # Créer de nouveaux emprunts avec des participants
    participants = Participant.objects.all()
    if participants.exists():
        from BookApp.models import Book
        from datetime import date, timedelta
        
        books = Book.objects.all()
        if books.exists():
            # Créer quelques emprunts de test
            borrowing1 = Borrowing.objects.create(
                book=books[0],
                participant=participants[0],
                return_date=date.today() + timedelta(days=10),
                returned=False
            )
            print(f"Emprunt créé: {books[0].title} par {participants[0].first_name}")
            
            if len(books) > 1 and len(participants) > 1:
                borrowing2 = Borrowing.objects.create(
                    book=books[1],
                    participant=participants[1],
                    return_date=date.today() - timedelta(days=5),
                    returned=True
                )
                print(f"Emprunt créé: {books[1].title} par {participants[1].first_name}")
    
    print("Mise à jour terminée!")

if __name__ == '__main__':
    update_borrowings()

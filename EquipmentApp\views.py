from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from .models import Equipment, Rental
from TypeApp.models import Type
from datetime import date, timedelta

# Create your views here.

def equipment_list(request):
    """Affiche la liste des équipements avec filtres et pagination"""
    equipments = Equipment.objects.all().order_by('quantity')

    # Filtres
    search_query = request.GET.get('search', '')
    type_filter = request.GET.get('type', '')
    status_filter = request.GET.get('status', '')

    if search_query:
        equipments = equipments.filter(
            Q(name__icontains=search_query) |
            Q(brand__icontains=search_query)
        )

    if type_filter:
        equipments = equipments.filter(type_id=type_filter)

    if status_filter:
        equipments = equipments.filter(status=status_filter)

    # Pagination
    paginator = Paginator(equipments, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Données pour les filtres
    types = Type.objects.all()
    status_choices = Equipment.STATUS_CHOICES

    context = {
        'page_obj': page_obj,
        'types': types,
        'status_choices': status_choices,
        'search_query': search_query,
        'type_filter': type_filter,
        'status_filter': status_filter,
    }

    return render(request, 'equipment/equipment_list.html', context)

@login_required
def my_rentals(request):
    """Affiche les locations de l'utilisateur connecté"""
    try:
        rentals = Rental.objects.filter(customer=request.user).order_by('-rental_date')

        if not rentals.exists():
            messages.info(request, "Vous n'avez encore loué aucun équipement.")

    except Exception as e:
        rentals = []
        messages.warning(request, f"Erreur lors de la récupération des locations: {str(e)}")

    context = {
        'rentals': rentals,
    }
    return render(request, 'equipment/my_rentals.html', context)

@login_required
def rent_equipment(request, equipment_id):
    """Simule la location d'un équipement"""
    equipment = get_object_or_404(Equipment, id=equipment_id)

    if not equipment.is_available():
        messages.error(request, f"L'équipement '{equipment.name}' n'est pas disponible pour la location.")
        return redirect('equipment_list')

    # Simulation de location (pour l'instant)
    messages.success(request, f"Simulation: Vous avez loué '{equipment.name}' avec succès. Cette fonctionnalité sera complètement opérationnelle après configuration.")
    return redirect('equipment_list')

def show_equipment_rentals(request, equipment_id):
    """Affiche les locations d'un équipement spécifique"""
    equipment = get_object_or_404(Equipment, id=equipment_id)
    rentals = Rental.objects.filter(equipment=equipment).order_by('-rental_date')

    context = {
        'equipment': equipment,
        'rentals': rentals,
    }
    return render(request, 'equipment/show_rentals.html', context)

#!/usr/bin/env python
"""
Script pour créer des données de test pour l'application de bibliothèque
"""
import os
import sys
import django
from datetime import date, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'MyProject.settings')
django.setup()

from django.contrib.auth import get_user_model
from CategoryApp.models import Category
from ClientApp.models import Client
from BookApp.models import Book, Borrowing

def create_test_data():
    print("Création des données de test...")
    
    # Créer des catégories
    categories_data = [
        "Littérature",
        "Sciences humaines", 
        "Technologie",
        "Histoire",
        "Roman",
        "Mystère",
        "Aventure"
    ]
    
    categories = []
    for cat_name in categories_data:
        category, created = Category.objects.get_or_create(category_name=cat_name)
        categories.append(category)
        if created:
            print(f"Catégorie créée: {cat_name}")
    
    # Créer des clients
    clients_data = [
        {
            'library_identifier': 'Aabcde1234',
            'first_name': '<PERSON>',
            'last_name': '<PERSON><PERSON>',
            'email': '<EMAIL>',
            'username': 'jean_dupont'
        },
        {
            'library_identifier': 'Bfghij5678',
            'first_name': 'Marie',
            'last_name': 'Martin',
            'email': '<EMAIL>',
            'username': 'marie_martin'
        },
        {
            'library_identifier': 'Cklmno9012',
            'first_name': 'Pierre',
            'last_name': 'Durand',
            'email': '<EMAIL>',
            'username': 'pierre_durand'
        }
    ]
    
    clients = []
    for client_data in clients_data:
        client, created = Client.objects.get_or_create(
            library_identifier=client_data['library_identifier'],
            defaults=client_data
        )
        if created:
            print(f"Client créé: {client_data['first_name']} {client_data['last_name']}")
        clients.append(client)
    
    # Créer des livres
    books_data = [
        {
            'title': 'How to Solve Your Own Murder',
            'author': 'Jessie Q. Sutanto',
            'publication_date': date(2024, 2, 1),
            'category': categories[5]  # Mystère
        },
        {
            'title': 'The Last Murder at the End of the World',
            'author': 'David Rosenfelt',
            'publication_date': date(2024, 1, 1),
            'category': categories[4]  # Roman
        },
        {
            'title': 'Pride and Prejudice',
            'author': 'Jane Austen',
            'publication_date': date(1813, 1, 1),
            'category': categories[6]  # Aventure
        },
        {
            'title': 'Les Misérables',
            'author': 'Victor Hugo',
            'publication_date': date(1862, 1, 1),
            'category': categories[0]  # Littérature
        },
        {
            'title': 'Introduction à Python',
            'author': 'Guido van Rossum',
            'publication_date': date(2020, 1, 1),
            'category': categories[2]  # Technologie
        },
        {
            'title': 'Histoire de France',
            'author': 'Jules Michelet',
            'publication_date': date(1855, 1, 1),
            'category': categories[3]  # Histoire
        }
    ]
    
    books = []
    for book_data in books_data:
        book, created = Book.objects.get_or_create(
            title=book_data['title'],
            defaults=book_data
        )
        if created:
            print(f"Livre créé: {book_data['title']}")
        books.append(book)
    
    # Créer quelques emprunts
    borrowings_data = [
        {
            'book': books[0],  # How to Solve Your Own Murder
            'client': clients[0],  # Jean Dupont
            'return_date': date.today() + timedelta(days=10),
            'returned': False
        },
        {
            'book': books[1],  # The Last Murder at the End of the World
            'client': clients[1],  # Marie Martin
            'return_date': date.today() - timedelta(days=5),
            'returned': True
        },
        {
            'book': books[2],  # Pride and Prejudice
            'client': clients[2],  # Pierre Durand
            'return_date': date.today() + timedelta(days=7),
            'returned': False
        }
    ]
    
    for borrowing_data in borrowings_data:
        borrowing, created = Borrowing.objects.get_or_create(
            book=borrowing_data['book'],
            client=borrowing_data['client'],
            defaults=borrowing_data
        )
        if created:
            print(f"Emprunt créé: {borrowing_data['book'].title} par {borrowing_data['client'].first_name}")
    
    print("Données de test créées avec succès!")
    print("\nClients créés:")
    for client in clients:
        print(f"- {client.first_name} {client.last_name} ({client.library_identifier})")

if __name__ == '__main__':
    create_test_data()

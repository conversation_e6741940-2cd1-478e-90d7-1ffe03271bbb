#!/usr/bin/env python
"""
Script pour créer les données de test pour le système d'équipements
"""
import os
import django
from datetime import date, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'MyProject.settings')
django.setup()

from CustomerApp.models import Customer
from TypeApp.models import Type
from EquipmentApp.models import Equipment, <PERSON><PERSON>

def create_test_data():
    print("🚀 Création des données de test pour le système d'équipements...")
    
    # 1. Créer un superutilisateur admin
    print("👤 Création du superutilisateur admin...")
    try:
        admin_user = Customer.objects.create_superuser(
            customer_id='ADMI1234',
            username='admin',
            email='<EMAIL>',
            first_name='Admin',
            last_name='System',
            password='admin123'
        )
        print(f"✅ Superutilisateur créé: {admin_user.username}")
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'admin: {e}")
    
    # 2. Créer des types d'équipements
    print("🏷️ Création des types d'équipements...")
    types_data = [
        {'name': 'Informatique', 'description': 'Équipements informatiques et électroniques'},
        {'name': 'Audiovisuel', 'description': 'Matériel audio et vidéo'},
        {'name': 'Mobilier', 'description': 'Mobilier et équipements de bureau'},
        {'name': 'Véhicules', 'description': 'Véhicules et moyens de transport'},
        {'name': 'Outils', 'description': 'Outils et équipements techniques'},
    ]
    
    for type_data in types_data:
        type_obj, created = Type.objects.get_or_create(
            name=type_data['name'],
            defaults={'description': type_data['description']}
        )
        if created:
            print(f"✅ Type créé: {type_obj.name}")
    
    # 3. Créer des équipements
    print("🔧 Création des équipements...")
    equipments_data = [
        {'name': 'Ordinateur Portable Dell', 'brand': 'Dell', 'type': 'Informatique', 'quantity': 5, 'status': 'AVAILABLE'},
        {'name': 'Projecteur HD', 'brand': 'Epson', 'type': 'Audiovisuel', 'quantity': 3, 'status': 'AVAILABLE'},
        {'name': 'Caméra Professionnelle', 'brand': 'Canon', 'type': 'Audiovisuel', 'quantity': 2, 'status': 'AVAILABLE'},
        {'name': 'Bureau Ergonomique', 'brand': 'IKEA', 'type': 'Mobilier', 'quantity': 10, 'status': 'AVAILABLE'},
        {'name': 'Véhicule Utilitaire', 'brand': 'Renault', 'type': 'Véhicules', 'quantity': 2, 'status': 'AVAILABLE'},
        {'name': 'Perceuse Électrique', 'brand': 'Bosch', 'type': 'Outils', 'quantity': 4, 'status': 'AVAILABLE'},
        {'name': 'Tablette iPad', 'brand': 'Apple', 'type': 'Informatique', 'quantity': 8, 'status': 'AVAILABLE'},
        {'name': 'Micro-ondes', 'brand': 'Samsung', 'type': 'Mobilier', 'quantity': 1, 'status': 'MAINTENANCE'},
    ]
    
    for eq_data in equipments_data:
        try:
            type_obj = Type.objects.get(name=eq_data['type'])
            equipment, created = Equipment.objects.get_or_create(
                name=eq_data['name'],
                brand=eq_data['brand'],
                defaults={
                    'type': type_obj,
                    'quantity': eq_data['quantity'],
                    'status': eq_data['status']
                }
            )
            if created:
                print(f"✅ Équipement créé: {equipment.name} - {equipment.brand}")
        except Exception as e:
            print(f"❌ Erreur pour {eq_data['name']}: {e}")
    
    # 4. Créer des clients de test
    print("👥 Création des clients de test...")
    customers_data = [
        {'customer_id': 'JEAN1234', 'username': 'jean_dupont', 'first_name': 'Jean', 'last_name': 'Dupont', 'email': '<EMAIL>'},
        {'customer_id': 'MARI5678', 'username': 'marie_martin', 'first_name': 'Marie', 'last_name': 'Martin', 'email': '<EMAIL>'},
        {'customer_id': 'PIER9012', 'username': 'pierre_durand', 'first_name': 'Pierre', 'last_name': 'Durand', 'email': '<EMAIL>'},
    ]
    
    for cust_data in customers_data:
        try:
            customer, created = Customer.objects.get_or_create(
                customer_id=cust_data['customer_id'],
                defaults={
                    'username': cust_data['username'],
                    'first_name': cust_data['first_name'],
                    'last_name': cust_data['last_name'],
                    'email': cust_data['email']
                }
            )
            if created:
                customer.set_password('password123')
                customer.save()
                print(f"✅ Client créé: {customer.first_name} {customer.last_name} ({customer.customer_id})")
        except Exception as e:
            print(f"❌ Erreur pour {cust_data['customer_id']}: {e}")
    
    # 5. Créer des locations de test
    print("📋 Création des locations de test...")
    try:
        customers = Customer.objects.filter(is_superuser=False)
        equipments = Equipment.objects.filter(status='AVAILABLE')[:3]
        
        for i, customer in enumerate(customers):
            if i < len(equipments):
                equipment = equipments[i]
                rental = Rental.objects.create(
                    equipment=equipment,
                    customer=customer,
                    return_date=date.today() + timedelta(days=30),
                    returned=i == 0  # Premier retourné, autres en cours
                )
                status = "Retourné" if rental.returned else "En cours"
                print(f"✅ Location créée: {customer.first_name} {customer.last_name} - {equipment.name} ({status})")
    except Exception as e:
        print(f"❌ Erreur lors de la création des locations: {e}")
    
    print("🎉 Données de test créées avec succès!")
    print("\n📋 Résumé:")
    print(f"   - Types: {Type.objects.count()}")
    print(f"   - Équipements: {Equipment.objects.count()}")
    print(f"   - Clients: {Customer.objects.count()}")
    print(f"   - Locations: {Rental.objects.count()}")
    
    print("\n🔐 Comptes de test:")
    print("   Admin: admin / admin123")
    print("   Clients: jean_dupont, marie_martin, pierre_durand / password123")

if __name__ == '__main__':
    create_test_data()

{% extends 'base.html' %}

{% block title %}Liste des livres - Bibliothèque{% endblock %}

{% block header %}Liste des livres{% endblock %}

{% block content %}
<div class="filters">
    <h3>Filtres</h3>
    <form method="get">
        <div class="filter-group">
            <label for="title">Titre:</label>
            <input type="text" id="title" name="title" value="{{ current_filters.title|default:'' }}" placeholder="Rechercher par titre">
        </div>
        
        <div class="filter-group">
            <label for="category">Catégorie:</label>
            <select id="category" name="category">
                <option value="">Toutes les catégories</option>
                {% for category in categories %}
                    <option value="{{ category.id }}" {% if current_filters.category == category.id|stringformat:"s" %}selected{% endif %}>
                        {{ category.category_name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="filter-group">
            <label for="availability">Disponibilité:</label>
            <select id="availability" name="availability">
                <option value="">Tous les livres</option>
                <option value="available" {% if current_filters.availability == 'available' %}selected{% endif %}>Disponibles</option>
                <option value="unavailable" {% if current_filters.availability == 'unavailable' %}selected{% endif %}>Non disponibles</option>
            </select>
        </div>
        
        <button type="submit" class="btn">Filtrer</button>
        <a href="{% url 'book_list' %}" class="btn btn-danger">Réinitialiser</a>
    </form>
</div>

{% if page_obj %}
    <div class="book-list">
        {% for book in page_obj %}
            <div class="book-card">
                <div class="book-title">{{ book.title }}</div>
                <div class="book-author">par {{ book.author }}</div>
                <div class="book-date">Publié le {{ book.publication_date|date:"d/m/Y" }}</div>
                <div class="book-category">{{ book.category.category_name }}</div>
                
                {% if book.is_available %}
                    <div class="book-status available">✓ Disponible</div>
                    {% if user.is_authenticated %}
                        <a href="{% url 'borrow_book' book.id %}" class="btn btn-success">Emprunter</a>
                    {% else %}
                        <p><a href="{% url 'login' %}">Connectez-vous</a> pour emprunter ce livre</p>
                    {% endif %}
                {% else %}
                    <div class="book-status unavailable">✗ Non disponible</div>
                    <p>Livre actuellement emprunté</p>
                {% endif %}
                
                <a href="{% url 'book_borrowings' book.id %}" class="btn">Afficher Emprunts</a>
                
                {% if book.book_file %}
                    <a href="{{ book.book_file.url }}" class="btn" target="_blank">Télécharger</a>
                {% endif %}
            </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <div class="pagination" style="text-align: center; margin: 2rem 0;">
            {% if page_obj.has_previous %}
                <a href="?page=1{% if current_filters.title %}&title={{ current_filters.title }}{% endif %}{% if current_filters.category %}&category={{ current_filters.category }}{% endif %}{% if current_filters.availability %}&availability={{ current_filters.availability }}{% endif %}" class="btn">« Première</a>
                <a href="?page={{ page_obj.previous_page_number }}{% if current_filters.title %}&title={{ current_filters.title }}{% endif %}{% if current_filters.category %}&category={{ current_filters.category }}{% endif %}{% if current_filters.availability %}&availability={{ current_filters.availability }}{% endif %}" class="btn">‹ Précédente</a>
            {% endif %}

            <span style="margin: 0 1rem;">
                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if current_filters.title %}&title={{ current_filters.title }}{% endif %}{% if current_filters.category %}&category={{ current_filters.category }}{% endif %}{% if current_filters.availability %}&availability={{ current_filters.availability }}{% endif %}" class="btn">Suivante ›</a>
                <a href="?page={{ page_obj.paginator.num_pages }}{% if current_filters.title %}&title={{ current_filters.title }}{% endif %}{% if current_filters.category %}&category={{ current_filters.category }}{% endif %}{% if current_filters.availability %}&availability={{ current_filters.availability }}{% endif %}" class="btn">Dernière »</a>
            {% endif %}
        </div>
    {% endif %}
{% else %}
    <p>Aucun livre trouvé.</p>
{% endif %}
{% endblock %}

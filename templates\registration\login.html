{% extends 'base.html' %}

{% block title %}Connexion - Système de Location{% endblock %}

{% block header %}Connexion{% endblock %}

{% block content %}
<div class="login-form">
    <h2 style="text-align: center; margin-bottom: 2rem; color: #2c3e50;">Se connecter</h2>
    
    {% if form.errors %}
        <div class="message error">
            <strong>Erreur de connexion:</strong>
            {% for field, errors in form.errors.items %}
                {% for error in errors %}
                    <br>{{ error }}
                {% endfor %}
            {% endfor %}
        </div>
    {% endif %}
    
    <form method="post">
        {% csrf_token %}
        
        <div class="form-group">
            <label for="{{ form.username.id_for_label }}">Nom d'utilisateur:</label>
            {{ form.username }}
        </div>
        
        <div class="form-group">
            <label for="{{ form.password.id_for_label }}">Mot de passe:</label>
            {{ form.password }}
        </div>
        
        <div class="form-group">
            <button type="submit" class="btn" style="width: 100%;">Se connecter</button>
        </div>
    </form>
    
    <div style="text-align: center; margin-top: 1rem;">
        <p>Pas encore de compte ? Contactez l'administrateur pour créer un compte client.</p>
        <a href="{% url 'equipment_list' %}">Retour à la liste des équipements</a>
    </div>
</div>

{% if not user.is_authenticated %}
<div style="background: white; padding: 2rem; border-radius: 8px; margin: 2rem auto; max-width: 600px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h3 style="color: #2c3e50; text-align: center;">Informations importantes</h3>
    <div style="background-color: #d1ecf1; padding: 1rem; border-radius: 4px; margin: 1rem 0;">
        <h4 style="color: #0c5460; margin-top: 0;">Si vous n'êtes pas connecté:</h4>
        <p style="color: #0c5460; margin-bottom: 0;">Vous pouvez consulter la liste des équipements mais vous ne pourrez pas les louer. Un message "aucune location" s'affichera.</p>
    </div>

    <div style="background-color: #d4edda; padding: 1rem; border-radius: 4px; margin: 1rem 0;">
        <h4 style="color: #155724; margin-top: 0;">Si vous êtes connecté:</h4>
        <p style="color: #155724; margin-bottom: 0;">Vous verrez un message de bienvenue avec votre nom et pourrez louer des équipements disponibles.</p>
    </div>
</div>
{% endif %}
{% endblock %}

<!DOCTYPE html>
<html>
<head>
    <title>Locations - {{ equipment.name }}</title>
</head>
<body>
    <h1>Locations pour: {{ equipment.name }}</h1>
    
    <p><a href="{% url 'equipment_list' %}">Retour à la liste des équipements</a></p>

    <h2>Informations de l'équipement</h2>
    <table border="1">
        <tr>
            <th>Name</th>
            <th>Brand</th>
            <th>Type</th>
            <th>Status</th>
            <th>Available Units</th>
        </tr>
        <tr>
            <td>{{ equipment.name }}</td>
            <td>{{ equipment.brand }}</td>
            <td>{{ equipment.type.name }}</td>
            <td>{{ equipment.get_status_display }}</td>
            <td>{{ equipment.get_available_quantity }}</td>
        </tr>
    </table>

    <h2>Historique des locations</h2>
    {% if rentals %}
        <table border="1">
            <tr>
                <th>Client</th>
                <th>Date location</th>
                <th>Date retour prévue</th>
                <th>Statut</th>
            </tr>
            {% for rental in rentals %}
            <tr>
                <td>{{ rental.customer.first_name }} {{ rental.customer.last_name }} ({{ rental.customer.customer_id }})</td>
                <td>{{ rental.rental_date|date:"d/m/Y" }}</td>
                <td>{{ rental.return_date|date:"d/m/Y" }}</td>
                <td>{% if rental.returned %}Retourné{% else %}En cours{% endif %}</td>
            </tr>
            {% endfor %}
        </table>
    {% else %}
        <p>Aucune location pour cet équipement.</p>
    {% endif %}
</body>
</html>

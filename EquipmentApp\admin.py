from django.contrib import admin
from .models import Equipment, Rental

# Register your models here.

class RentalInline(admin.TabularInline):
    """Affichage des locations dans le formulaire d'ajout/modification d'équipement"""
    model = Rental
    extra = 0
    readonly_fields = ('rental_date',)
    fields = ('customer', 'rental_date', 'return_date', 'returned')

class AvailabilityFilter(admin.SimpleListFilter):
    """Filtre par disponibilité"""
    title = 'Disponibilité'
    parameter_name = 'availability'

    def lookups(self, request, model_admin):
        return (
            ('available', 'Disponible'),
            ('rented', 'Loué'),
            ('maintenance', 'En maintenance'),
            ('out_of_order', 'Hors service'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'available':
            return queryset.filter(status='AVAILABLE')
        elif self.value() == 'rented':
            return queryset.filter(status='RENTED')
        elif self.value() == 'maintenance':
            return queryset.filter(status='MAINTENANCE')
        elif self.value() == 'out_of_order':
            return queryset.filter(status='OUT_OF_ORDER')
        return queryset

@admin.register(Equipment)
class EquipmentAdmin(admin.ModelAdmin):
    list_display = ('name', 'brand', 'type', 'quantity', 'status', 'get_available_quantity', 'created_at')
    search_fields = ('name', 'brand', 'type__name')
    list_filter = ('type', 'status', AvailabilityFilter, 'created_at')
    list_per_page = 20
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('name', 'brand')
    autocomplete_fields = ('type',)
    inlines = [RentalInline]

    def get_available_quantity(self, obj):
        """Affiche la quantité disponible"""
        return obj.get_available_quantity()
    get_available_quantity.short_description = 'Quantité disponible'

    fieldsets = (
        ('Informations de l\'équipement', {
            'fields': ('name', 'brand', 'type', 'quantity', 'status')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Rental)
class RentalAdmin(admin.ModelAdmin):
    list_display = ('equipment', 'customer', 'rental_date', 'return_date', 'returned')
    list_filter = ('returned', 'rental_date', 'return_date', 'equipment__type')
    search_fields = ('equipment__name', 'customer__first_name', 'customer__last_name', 'customer__customer_id')
    readonly_fields = ('rental_date',)
    ordering = ('-rental_date',)
    autocomplete_fields = ('equipment', 'customer')

    fieldsets = (
        ('Informations de la location', {
            'fields': ('equipment', 'customer', 'rental_date', 'return_date', 'returned')
        }),
    )

# Generated by Django 5.2.3 on 2025-06-28 16:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('CategoryApp', '0001_initial'),
        ('ClientApp', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Book',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('author', models.CharField(max_length=200)),
                ('publication_date', models.DateField()),
                ('book_file', models.FileField(blank=True, null=True, upload_to='books/')),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='books', to='CategoryApp.category')),
            ],
            options={
                'verbose_name': 'Livre',
                'verbose_name_plural': 'Livres',
                'ordering': ['-publication_date'],
            },
        ),
        migrations.CreateModel(
            name='Borrowing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('borrowing_date', models.DateField(auto_now_add=True)),
                ('return_date', models.DateField()),
                ('returned', models.BooleanField(default=False)),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='borrowings', to='BookApp.book')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='borrowings', to='ClientApp.client')),
            ],
            options={
                'verbose_name': 'Emprunt',
                'verbose_name_plural': 'Emprunts',
                'ordering': ['-borrowing_date'],
            },
        ),
    ]

# Generated by Django 5.2.3 on 2025-06-30 18:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('TypeApp', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Equipment',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('brand', models.CharField(max_length=100)),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('status', models.CharField(choices=[('AVAILABLE', 'Disponible'), ('RENTED', 'Loué'), ('MAINTENANCE', 'En maintenance'), ('OUT_OF_ORDER', 'Hors service')], default='AVAILABLE', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='equipments', to='TypeApp.type')),
            ],
            options={
                'verbose_name': 'Équipement',
                'verbose_name_plural': 'Équipements',
                'ordering': ['name', 'brand'],
            },
        ),
        migrations.CreateModel(
            name='Rental',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('rental_date', models.DateField(auto_now_add=True)),
                ('return_date', models.DateField()),
                ('returned', models.BooleanField(default=False)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rentals', to=settings.AUTH_USER_MODEL)),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rentals', to='EquipmentApp.equipment')),
            ],
            options={
                'verbose_name': 'Location',
                'verbose_name_plural': 'Locations',
                'ordering': ['-rental_date'],
            },
        ),
    ]

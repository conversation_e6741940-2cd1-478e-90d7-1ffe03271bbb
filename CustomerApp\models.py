from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator

# Create your models here.

def gmail_validator(value):
    """Valide que l'email est un domaine gmail.com"""
    if not value.endswith('@gmail.com'):
        raise ValidationError('Veuillez entrer une adresse email valide appartenant au domaine gmail.com (ex : <EMAIL>).')

def customer_id_validator(value):
    """Valide le format du customer_id: 4 lettres + 4 chiffres (ex: AAAA1111)"""
    import re
    pattern = r'^[A-Z]{4}\d{4}$'
    if not re.match(pattern, value):
        raise ValidationError('Le customer_id doit respecter le format: 4 lettres majuscules suivies de 4 chiffres (ex: AAAA1111)')

class Customer(AbstractUser):
    customer_id = models.CharField(
        primary_key=True,
        max_length=8,
        unique=True,
        validators=[customer_id_validator],
        help_text="Format: 4 lettres majuscules + 4 chiffres (ex: AAAA1111)"
    )
    first_name = models.CharField(max_length=200)
    last_name = models.CharField(max_length=200)
    email = models.EmailField(
        max_length=250,
        unique=True,
        validators=[gmail_validator],
        help_text="Adresse email valide du domaine gmail.com"
    )
    username = models.CharField(max_length=150, unique=True)

    USERNAME_FIELD = 'username'
    REQUIRED_FIELDS = ['email', 'first_name', 'last_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.customer_id})"

    class Meta:
        verbose_name = "Customer"
        verbose_name_plural = "Customers"

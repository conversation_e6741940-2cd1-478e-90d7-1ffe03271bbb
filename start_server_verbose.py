#!/usr/bin/env python
"""
Script pour démarrer le serveur Django avec des logs visibles
"""
import os
import sys
import subprocess

def start_server():
    print("🚀 Démarrage du serveur Django...")
    print("📁 Répertoire de travail:", os.getcwd())
    
    # Vérifier que manage.py existe
    if not os.path.exists('manage.py'):
        print("❌ Erreur: manage.py non trouvé dans le répertoire actuel")
        return
    
    # Vérifier que l'environnement virtuel existe
    python_path = os.path.join('venv', 'Scripts', 'python.exe')
    if not os.path.exists(python_path):
        print("❌ Erreur: Environnement virtuel non trouvé")
        return
    
    print("✅ Fichiers trouvés, démarrage du serveur...")
    
    try:
        # Démarrer le serveur Django
        cmd = [python_path, 'manage.py', 'runserver', '127.0.0.1:8000', '--verbosity=2']
        print(f"🔧 Commande: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print("📡 Serveur en cours de démarrage...")
        
        # Afficher les logs en temps réel
        for line in process.stdout:
            print(line.strip())
            if "Starting development server" in line:
                print("🎉 Serveur démarré avec succès!")
                print("🌐 Accédez à: http://127.0.0.1:8000/")
                break
                
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")

if __name__ == '__main__':
    start_server()

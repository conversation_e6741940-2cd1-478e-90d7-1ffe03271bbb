{% extends 'base.html' %}

{% block title %}Liste des Équipements - Système de Location{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">📋 Liste des Équipements</h1>
                <div class="d-flex align-items-center">
                    {% if user.is_authenticated %}
                        <span class="me-3">Bienvenue, {{ user.first_name }} {{ user.last_name }}</span>
                        <a href="{% url 'my_rentals' %}" class="btn btn-outline-primary me-2">Mes Locations</a>
                        <a href="{% url 'logout' %}" class="btn btn-outline-secondary">Se déconnecter</a>
                    {% else %}
                        <a href="{% url 'login' %}" class="btn btn-primary">Se connecter</a>
                    {% endif %}
                </div>
            </div>

            <!-- Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <!-- Filtres -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">Rechercher</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ search_query }}" placeholder="Nom ou marque...">
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">Type</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">Tous les types</option>
                                {% for type in types %}
                                    <option value="{{ type.id }}" {% if type.id|stringformat:"s" == type_filter %}selected{% endif %}>
                                        {{ type.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Statut</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Tous les statuts</option>
                                {% for status_code, status_label in status_choices %}
                                    <option value="{{ status_code }}" {% if status_code == status_filter %}selected{% endif %}>
                                        {{ status_label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">Filtrer</button>
                            <a href="{% url 'equipment_list' %}" class="btn btn-outline-secondary">Reset</a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Liste des équipements -->
            <div class="row">
                {% for equipment in page_obj %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">{{ equipment.name }}</h5>
                                <p class="card-text">
                                    <strong>Marque:</strong> {{ equipment.brand }}<br>
                                    <strong>Type:</strong> {{ equipment.type.name }}<br>
                                    <strong>Quantité:</strong> {{ equipment.quantity }}<br>
                                    <strong>Disponible:</strong> {{ equipment.get_available_quantity }}<br>
                                    <span class="badge 
                                        {% if equipment.status == 'AVAILABLE' %}bg-success
                                        {% elif equipment.status == 'RENTED' %}bg-warning
                                        {% elif equipment.status == 'MAINTENANCE' %}bg-info
                                        {% else %}bg-danger{% endif %}">
                                        {{ equipment.get_status_display }}
                                    </span>
                                </p>
                            </div>
                            <div class="card-footer">
                                {% if user.is_authenticated and equipment.is_available %}
                                    <a href="{% url 'rent_equipment' equipment.id %}" class="btn btn-success btn-sm">
                                        🔄 Louer
                                    </a>
                                {% elif not user.is_authenticated %}
                                    <a href="{% url 'login' %}" class="btn btn-outline-primary btn-sm">
                                        Connectez-vous pour louer
                                    </a>
                                {% else %}
                                    <button class="btn btn-secondary btn-sm" disabled>
                                        Non disponible
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <h4>Aucun équipement trouvé</h4>
                            <p>Aucun équipement ne correspond à vos critères de recherche.</p>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="Pagination des équipements">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Premier</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Précédent</a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Suivant</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if type_filter %}&type={{ type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Dernier</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

<!DOCTYPE html>
<html>
<head>
    <title>Liste des Équipements</title>
</head>
<body>
    <h1>Liste des Équipements</h1>

    {% if user.is_authenticated %}
        <p>Bienvenue, {{ user.first_name }} {{ user.last_name }} |
        <a href="{% url 'my_rentals' %}">Mes Locations</a> |
        <a href="{% url 'logout' %}">Se déconnecter</a></p>
    {% else %}
        <p><a href="{% url 'login' %}">Se connecter</a></p>
    {% endif %}

    {% if messages %}
        {% for message in messages %}
            <p>{{ message }}</p>
        {% endfor %}
    {% endif %}

    <table border="1">
        <tr>
            <th>Name</th>
            <th>Brand</th>
            <th>Type</th>
            <th>Status</th>
            <th>Available Units</th>
            <th>Action</th>
        </tr>
        {% for equipment in page_obj %}
        <tr>
            <td>{{ equipment.name }}</td>
            <td>{{ equipment.brand }}</td>
            <td>{{ equipment.type.name }}</td>
            <td>{{ equipment.get_status_display }}</td>
            <td>{{ equipment.get_available_quantity }}</td>
            <td>
                {% if user.is_authenticated and equipment.is_available %}
                    <a href="{% url 'rent_equipment' equipment.id %}">Louer</a>
                {% elif not user.is_authenticated %}
                    <a href="{% url 'login' %}">Se connecter</a>
                {% else %}
                    Non disponible
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6">Aucun équipement trouvé</td>
        </tr>
        {% endfor %}
    </table>

    {% if page_obj.has_other_pages %}
        <p>
            {% if page_obj.has_previous %}
                <a href="?page=1">Premier</a> |
                <a href="?page={{ page_obj.previous_page_number }}">Précédent</a> |
            {% endif %}
            Page {{ page_obj.number }} sur {{ page_obj.paginator.num_pages }}
            {% if page_obj.has_next %}
                | <a href="?page={{ page_obj.next_page_number }}">Suivant</a> |
                <a href="?page={{ page_obj.paginator.num_pages }}">Dernier</a>
            {% endif %}
        </p>
    {% endif %}
</body>
</html>

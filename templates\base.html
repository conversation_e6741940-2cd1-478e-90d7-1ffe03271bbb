<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Système de Location d'Équipements{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        .header h1 {
            margin: 0;
            text-align: center;
        }
        .nav {
            background-color: #34495e;
            padding: 1rem;
            text-align: center;
        }
        .nav a {
            color: white;
            text-decoration: none;
            margin: 0 1rem;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .nav a:hover {
            background-color: #2c3e50;
        }
        .messages {
            margin: 1rem 0;
        }
        .message {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 4px;
        }
        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .message.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .book-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .book-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .book-card:hover {
            transform: translateY(-2px);
        }
        .book-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }
        .book-author {
            color: #7f8c8d;
            margin-bottom: 0.5rem;
        }
        .book-date {
            color: #95a5a6;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        .book-category {
            background-color: #3498db;
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            display: inline-block;
            margin-bottom: 0.5rem;
        }
        .book-status {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .available {
            color: #27ae60;
        }
        .unavailable {
            color: #e74c3c;
        }
        .btn {
            background-color: #3498db;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-success {
            background-color: #27ae60;
        }
        .btn-success:hover {
            background-color: #229954;
        }
        .btn-danger {
            background-color: #e74c3c;
        }
        .btn-danger:hover {
            background-color: #c0392b;
        }
        .filters {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .filter-group {
            display: inline-block;
            margin-right: 1rem;
        }
        .filter-group label {
            font-weight: bold;
            margin-right: 0.5rem;
        }
        .login-form {
            max-width: 400px;
            margin: 2rem auto;
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .user-info {
            text-align: right;
            color: white;
            margin-bottom: 1rem;
        }
        .user-info a {
            color: #ecf0f1;
            text-decoration: none;
        }
        .user-info a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="header">
        {% if user.is_authenticated %}
            <div class="user-info">
                Bienvenue, {{ user.first_name }} {{ user.last_name }} | 
                <a href="{% url 'logout' %}">Se déconnecter</a>
            </div>
        {% else %}
            <div class="user-info">
                <a href="{% url 'login' %}">Se connecter</a>
            </div>
        {% endif %}
        <h1>{% block header %}🔧 Système de Location d'Équipements{% endblock %}</h1>
    </div>

    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{% url 'equipment_list' %}">🔧 Location</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{% url 'equipment_list' %}">Équipements</a>
                {% if user.is_authenticated %}
                    <a class="nav-link" href="{% url 'my_rentals' %}">Mes Locations</a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container">
        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="message {{ message.tags }}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        {% block content %}
        {% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

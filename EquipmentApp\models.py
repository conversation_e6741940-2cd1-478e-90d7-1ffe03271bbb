from django.db import models
from django.core.exceptions import ValidationError
from TypeApp.models import Type
from CustomerApp.models import Customer

# Create your models here.

class Equipment(models.Model):
    STATUS_CHOICES = [
        ('AVAILABLE', 'Disponible'),
        ('RENTED', 'Loué'),
        ('MAINTENANCE', 'En maintenance'),
        ('OUT_OF_ORDER', 'Hors service'),
    ]

    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=200)
    brand = models.CharField(max_length=100)
    quantity = models.PositiveIntegerField(default=1)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='AVAILABLE')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Relation avec Type
    type = models.ForeignKey(Type, on_delete=models.CASCADE, related_name='equipments')

    def __str__(self):
        return f"{self.name} - {self.brand}"

    def is_available(self):
        """Vérifie si l'équipement est disponible pour location"""
        return self.status == 'AVAILABLE' and self.quantity > 0

    def get_available_quantity(self):
        """Retourne la quantité disponible (non louée)"""
        rented_quantity = self.rentals.filter(returned=False).count()
        return max(0, self.quantity - rented_quantity)

    class Meta:
        verbose_name = "Équipement"
        verbose_name_plural = "Équipements"
        ordering = ['name', 'brand']


class Rental(models.Model):
    id = models.AutoField(primary_key=True)
    rental_date = models.DateField(auto_now_add=True)
    return_date = models.DateField()
    returned = models.BooleanField(default=False)

    # Relations
    equipment = models.ForeignKey(Equipment, on_delete=models.CASCADE, related_name='rentals')
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='rentals')

    def clean(self):
        """Validation personnalisée"""
        super().clean()
        if self.return_date and self.rental_date:
            if self.return_date < self.rental_date:
                raise ValidationError('La date de retour doit être postérieure ou égale à la date de location.')

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        status = "Retourné" if self.returned else "En cours"
        return f"{self.equipment.name} - {self.customer.first_name} {self.customer.last_name} ({status})"

    class Meta:
        verbose_name = "Location"
        verbose_name_plural = "Locations"
        ordering = ['-rental_date']

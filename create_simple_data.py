#!/usr/bin/env python
"""
Script simplifié pour créer des données de test
"""
import os
import sys
import django
from datetime import date, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'MyProject.settings')
django.setup()

from CategoryApp.models import Category
from ClientApp.models import Client
from BookApp.models import Book, Borrowing

def create_simple_data():
    print("Création des données de test...")
    
    # Créer des catégories
    categories = [
        Category.objects.get_or_create(category_name="Littérature")[0],
        Category.objects.get_or_create(category_name="Sciences humaines")[0], 
        Category.objects.get_or_create(category_name="Technologie")[0],
        Category.objects.get_or_create(category_name="Histoire")[0],
        Category.objects.get_or_create(category_name="Roman")[0],
        Category.objects.get_or_create(category_name="<PERSON><PERSON><PERSON>")[0],
        Category.objects.get_or_create(category_name="Aventure")[0]
    ]
    print("Catégories créées")
    
    # Créer des clients
    client1, created = Client.objects.get_or_create(
        library_identifier='Aabcde1234',
        defaults={
            'first_name': '<PERSON>',
            'last_name': 'Dupont',
            'email': '<EMAIL>',
            'username': 'jean_dupont'
        }
    )
    
    client2, created = Client.objects.get_or_create(
        library_identifier='Bfghij5678',
        defaults={
            'first_name': 'Marie',
            'last_name': 'Martin',
            'email': '<EMAIL>',
            'username': 'marie_martin'
        }
    )
    
    client3, created = Client.objects.get_or_create(
        library_identifier='Cklmno9012',
        defaults={
            'first_name': 'Pierre',
            'last_name': 'Durand',
            'email': '<EMAIL>',
            'username': 'pierre_durand'
        }
    )
    print("Clients créés")
    
    # Créer des livres
    book1, created = Book.objects.get_or_create(
        title='How to Solve Your Own Murder',
        defaults={
            'author': 'Jessie Q. Sutanto',
            'publication_date': date(2024, 2, 1),
            'category': categories[5]  # Mystère
        }
    )
    
    book2, created = Book.objects.get_or_create(
        title='The Last Murder at the End of the World',
        defaults={
            'author': 'David Rosenfelt',
            'publication_date': date(2024, 1, 1),
            'category': categories[4]  # Roman
        }
    )
    
    book3, created = Book.objects.get_or_create(
        title='Pride and Prejudice',
        defaults={
            'author': 'Jane Austen',
            'publication_date': date(1813, 1, 1),
            'category': categories[6]  # Aventure
        }
    )
    
    book4, created = Book.objects.get_or_create(
        title='Les Misérables',
        defaults={
            'author': 'Victor Hugo',
            'publication_date': date(1862, 1, 1),
            'category': categories[0]  # Littérature
        }
    )
    
    book5, created = Book.objects.get_or_create(
        title='Introduction à Python',
        defaults={
            'author': 'Guido van Rossum',
            'publication_date': date(2020, 1, 1),
            'category': categories[2]  # Technologie
        }
    )
    
    book6, created = Book.objects.get_or_create(
        title='Histoire de France',
        defaults={
            'author': 'Jules Michelet',
            'publication_date': date(1855, 1, 1),
            'category': categories[3]  # Histoire
        }
    )
    print("Livres créés")
    
    # Créer quelques emprunts
    borrowing1, created = Borrowing.objects.get_or_create(
        book=book1,
        client=client1,
        defaults={
            'return_date': date.today() + timedelta(days=10),
            'returned': False
        }
    )
    
    borrowing2, created = Borrowing.objects.get_or_create(
        book=book2,
        client=client2,
        defaults={
            'return_date': date.today() - timedelta(days=5),
            'returned': True
        }
    )
    
    borrowing3, created = Borrowing.objects.get_or_create(
        book=book3,
        client=client3,
        defaults={
            'return_date': date.today() + timedelta(days=7),
            'returned': False
        }
    )
    print("Emprunts créés")
    
    print("Données de test créées avec succès!")

if __name__ == '__main__':
    create_simple_data()

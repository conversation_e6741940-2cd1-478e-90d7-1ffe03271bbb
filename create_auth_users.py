#!/usr/bin/env python
"""
Script pour créer des utilisateurs qui peuvent se connecter
"""
import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'MyProject.settings')
django.setup()

from UserApp.models import Participant

def create_auth_users():
    print("Création des utilisateurs pour l'authentification...")
    
    # Créer des utilisateurs Participant qui peuvent se connecter
    users_data = [
        {
            'cin': '12345678',
            'username': 'jean_dupont',
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON><PERSON>',
            'participant_category': 'ETUDIANT',
            'password': 'password123'
        },
        {
            'cin': '87654321',
            'username': 'marie_martin',
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON>',
            'participant_category': 'ETUDIANT',
            'password': 'password123'
        },
        {
            'cin': '11223344',
            'username': 'pierre_durand',
            'email': '<EMAIL>',
            'first_name': 'Pierre',
            'last_name': 'Durand',
            'participant_category': 'ENSEIGNANT',
            'password': 'password123'
        }
    ]
    
    for user_data in users_data:
        password = user_data.pop('password')
        user, created = Participant.objects.get_or_create(
            cin=user_data['cin'],
            defaults=user_data
        )
        if created:
            user.set_password(password)
            user.save()
            print(f"Utilisateur créé: {user_data['username']} (mot de passe: {password})")
        else:
            print(f"Utilisateur existe déjà: {user_data['username']}")
    
    print("\nUtilisateurs créés avec succès!")
    print("Vous pouvez maintenant vous connecter avec:")
    print("- jean_dupont / password123")
    print("- marie_martin / password123") 
    print("- pierre_durand / password123")
    print("- admin / admin123 (superutilisateur)")

if __name__ == '__main__':
    create_auth_users()

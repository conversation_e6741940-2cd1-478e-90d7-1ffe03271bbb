{% extends 'base.html' %}

{% block title %}Mes emprunts - Bibliothèque{% endblock %}

{% block header %}Mes emprunts{% endblock %}

{% block content %}
{% if borrowings %}
    <div style="background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <h3>Historique de vos emprunts</h3>
        
        <table style="width: 100%; border-collapse: collapse; margin-top: 1rem;">
            <thead>
                <tr style="background-color: #f8f9fa;">
                    <th style="padding: 1rem; text-align: left; border-bottom: 2px solid #dee2e6;">Livre</th>
                    <th style="padding: 1rem; text-align: left; border-bottom: 2px solid #dee2e6;">Auteur</th>
                    <th style="padding: 1rem; text-align: left; border-bottom: 2px solid #dee2e6;">Date d'emprunt</th>
                    <th style="padding: 1rem; text-align: left; border-bottom: 2px solid #dee2e6;">Date de retour prévue</th>
                    <th style="padding: 1rem; text-align: left; border-bottom: 2px solid #dee2e6;">Statut</th>
                    <th style="padding: 1rem; text-align: left; border-bottom: 2px solid #dee2e6;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for borrowing in borrowings %}
                    <tr style="{% if not borrowing.returned %}background-color: #fff3cd;{% endif %}">
                        <td style="padding: 1rem; border-bottom: 1px solid #dee2e6;">
                            <strong>{{ borrowing.book.title }}</strong>
                        </td>
                        <td style="padding: 1rem; border-bottom: 1px solid #dee2e6;">
                            {{ borrowing.book.author }}
                        </td>
                        <td style="padding: 1rem; border-bottom: 1px solid #dee2e6;">
                            {{ borrowing.borrowing_date|date:"d/m/Y" }}
                        </td>
                        <td style="padding: 1rem; border-bottom: 1px solid #dee2e6;">
                            {{ borrowing.return_date|date:"d/m/Y" }}
                            {% if not borrowing.returned and borrowing.return_date < today %}
                                <br><small style="color: #e74c3c;">En retard</small>
                            {% endif %}
                        </td>
                        <td style="padding: 1rem; border-bottom: 1px solid #dee2e6;">
                            {% if borrowing.returned %}
                                <span class="available">Retourné</span>
                            {% else %}
                                <span class="unavailable">En cours</span>
                            {% endif %}
                        </td>
                        <td style="padding: 1rem; border-bottom: 1px solid #dee2e6;">
                            <a href="{% url 'book_borrowings' borrowing.book.id %}" class="btn" style="font-size: 0.8rem;">Voir détails</a>
                            {% if borrowing.book.book_file %}
                                <a href="{{ borrowing.book.book_file.url }}" class="btn" style="font-size: 0.8rem;" target="_blank">Télécharger</a>
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div style="background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
        <h3>Aucun emprunt</h3>
        <p>Vous n'avez encore emprunté aucun livre.</p>
        <a href="{% url 'book_list' %}" class="btn">Parcourir les livres</a>
    </div>
{% endif %}
{% endblock %}

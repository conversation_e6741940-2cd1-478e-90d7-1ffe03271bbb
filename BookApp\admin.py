from django.contrib import admin
from .models import Book, Borrowing
from CategoryApp.models import Category

# Register your models here.

class BorrowingInline(admin.TabularInline):
    """Affichage des emprunts dans le formulaire d'ajout/modification de livre"""
    model = Borrowing
    extra = 0
    readonly_fields = ('borrowing_date',)
    fields = ('participant', 'borrowing_date', 'return_date', 'returned')

class BorrowingCountFilter(admin.SimpleListFilter):
    """Filtre par nombre d'emprunts"""
    title = 'Nombre d\'emprunts'
    parameter_name = 'borrowing_count'

    def lookups(self, request, model_admin):
        return (
            ('no_borrowings', 'Pas d\'emprunts'),
            ('has_borrowings', 'Il y a des emprunts'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'no_borrowings':
            return queryset.filter(borrowings__isnull=True)
        elif self.value() == 'has_borrowings':
            return queryset.filter(borrowings__isnull=False).distinct()
        return queryset

@admin.register(Book)
class BookAdmin(admin.ModelAdmin):
    list_display = ('title', 'author', 'publication_date', 'category', 'book_file', 'is_available')
    search_fields = ('title',)
    list_filter = ('category', 'publication_date', BorrowingCountFilter)
    list_per_page = 10  # Pagination
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-publication_date',)
    autocomplete_fields = ('category',)
    inlines = [BorrowingInline]

    def is_available(self, obj):
        """Affiche si le livre est disponible"""
        return obj.is_available()
    is_available.boolean = True
    is_available.short_description = 'Disponible'

    fieldsets = (
        ('Informations du livre', {
            'fields': ('title', 'author', 'publication_date', 'category')
        }),
        ('Fichier', {
            'fields': ('book_file',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Borrowing)
class BorrowingAdmin(admin.ModelAdmin):
    list_display = ('book', 'participant', 'borrowing_date', 'return_date', 'returned')
    list_filter = ('returned', 'borrowing_date', 'return_date')
    search_fields = ('book__title', 'participant__first_name', 'participant__last_name', 'participant__username')
    readonly_fields = ('borrowing_date',)
    ordering = ('-borrowing_date',)

    fieldsets = (
        ('Informations de l\'emprunt', {
            'fields': ('book', 'participant', 'borrowing_date', 'return_date', 'returned')
        }),
    )

#!/usr/bin/env python
"""
Script pour associer les clients aux utilisateurs
"""
import os
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'MyProject.settings')
django.setup()

from ClientApp.models import Client
from UserApp.models import Participant

def link_users_clients():
    print("🔗 Association des clients aux utilisateurs...")
    
    # Récupérer tous les clients et utilisateurs
    clients = Client.objects.all()
    participants = Participant.objects.all()

    print(f"👥 Clients trouvés: {clients.count()}")
    print(f"🔐 Participants trouvés: {participants.count()}")

    # Associer les clients aux participants par nom d'utilisateur
    associations = [
        ('jean_du<PERSON>', '<PERSON>'),
        ('marie_martin', '<PERSON>'),
        ('pierre_du<PERSON>', '<PERSON>'),
    ]

    for username, client_name in associations:
        try:
            participant = Participant.objects.get(username=username)
            # Chercher le client par nom
            first_name, last_name = client_name.split(' ', 1)
            client = Client.objects.get(first_name=first_name, last_name=last_name)
            
            # Associer
            client.user = participant
            client.save()

            print(f"✅ {client_name} associé au participant {username}")

        except Participant.DoesNotExist:
            print(f"❌ Participant {username} non trouvé")
        except Client.DoesNotExist:
            print(f"❌ Client {client_name} non trouvé")
        except Exception as e:
            print(f"❌ Erreur pour {username}: {e}")
    
    print("🎉 Associations terminées!")

if __name__ == '__main__':
    link_users_clients()

#!/usr/bin/env python
"""
Script pour créer des emprunts de test
"""
import os
import django
from datetime import date, timedelta

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'MyProject.settings')
django.setup()

from BookApp.models import Book, Borrowing
from ClientApp.models import Client
from django.contrib.auth.models import User

def create_test_borrowings():
    print("📚 Création d'emprunts de test...")
    
    # Supprimer les emprunts existants
    Borrowing.objects.all().delete()
    print("🗑️ Emprunts existants supprimés")
    
    # Récupérer les clients et livres
    clients = Client.objects.all()
    books = Book.objects.all()
    
    print(f"👥 Clients trouvés: {clients.count()}")
    print(f"📖 Livres trouvés: {books.count()}")
    
    if not clients.exists():
        print("❌ Aucun client trouvé. Création d'un client de test...")
        # Créer un utilisateur et un client de test
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        if created:
            user.set_password('password123')
            user.save()
        
        client, created = Client.objects.get_or_create(
            username='test_user',
            defaults={
                'library_identifier': 'Atest1234',
                'first_name': 'Jean',
                'last_name': 'Dupont',
                'email': '<EMAIL>'
            }
        )
        clients = [client]
    
    if not books.exists():
        print("❌ Aucun livre trouvé")
        return
    
    # Créer des emprunts de test
    borrowings_created = 0
    
    for i, client in enumerate(clients[:3]):  # Maximum 3 clients
        for j, book in enumerate(books[:2]):  # Maximum 2 livres par client
            try:
                borrowing = Borrowing.objects.create(
                    book=book,
                    client=client,
                    return_date=date.today() + timedelta(days=30),
                    returned=j == 0  # Premier livre retourné, deuxième en cours
                )
                borrowings_created += 1
                status = "Retourné" if borrowing.returned else "En cours"
                print(f"✅ Emprunt créé: {client.first_name} {client.last_name} - {book.title} ({status})")
            except Exception as e:
                print(f"❌ Erreur lors de la création de l'emprunt: {e}")
    
    print(f"🎉 {borrowings_created} emprunts créés avec succès!")

if __name__ == '__main__':
    create_test_borrowings()

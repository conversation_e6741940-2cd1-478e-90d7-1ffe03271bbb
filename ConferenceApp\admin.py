from django.contrib import admin
from .models import *
# Register your models here.
class ConferenceDateFilter(admin.SimpleListFilter):
    title = 'Conference Date'
    parameter_name = 'conference_date'
    
    def lookups(self, request, model_admin):
        return (
            ('past', 'Past Conferences'),
            ('upcoming', 'Upcoming Conferences'),
            ('today', 'Today Conferences'),
            ('ongoing', 'Ongoing Conferences'),
        )
    
    def queryset(self, request, queryset):
        today = timezone.now().date()
        if self.value() == 'past':
            return queryset.filter(end_date__lt=today)
        if self.value() == 'upcoming':
            return queryset.filter(start_date__gt=today)
        if self.value() == 'today':
            return queryset.filter(start_date=today)
        if self.value() == 'ongoing':
            return queryset.filter(start_date__lte=today, end_date__gte=today)
        
class ParticipantFilter(admin.SimpleListFilter):
    title = "Participants Count"
    parameter_name = "participants"
    
    def lookups(self, request, model_admin):
        return (
            ('No', 'No reservations'),
            ('Yes', 'There are reservations')
                )
    def queryset(self, request, queryset):
        if self.value() == 'No':
            return queryset.filter(reservations__isnull == True)
        if self.value() == 'Yes':
            return queryset.filter(reservations__isnull == False)
        return queryset

class ConferenceAdmin(admin.ModelAdmin):
    list_display = ('title', 'location', 'start_date', 'category')
    search_fields = ('title', 'price',)
    list_per_page = 2
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('start_date','title')
    list_filter = ('category',ConferenceDateFilter)
    autocomplete_fields = ('category',)
    fieldsets = (
        ('Description', {
            'fields': ('title', 'description'),
        }),
        ('Horaires de la conférence', {
            'fields': ('start_date', 'end_date'),
        }),
        ('Informations de la conférence', {
            'fields': ('location', 'price', 'capacity'),
        }),
        ('Documents', {
            'fields': ('program',),
        }),
        ('Catégorie', {
            'fields': ('category',),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            
        }),
    )
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('conference', 'participant', 'reservation_date', 'confirmed')
    actions =["makeConfirmed", "makeNotConfirmed"]
    def makeConfirmed (self, request, queryset):
        queryset.update(connfirmed=True) 
        makeConfirmed.short_description = "Mark selected reservations as confirmed"

    def makeNotConfirmed (self, request, queryset):
            queryset.update(connfirmed=False) 
            makeNotConfirmed.short_description = "Mark selected reservations as not confirmed"

    search_fields = ('title',)
    
admin.site.register(Conference, ConferenceAdmin)
admin.site.register(Category, CategoryAdmin)
admin.site.register(Reservation)
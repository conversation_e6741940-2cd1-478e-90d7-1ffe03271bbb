from django.db import models
from django.core.exceptions import ValidationError
from CategoryApp.models import Category
from ClientApp.models import Client
from UserApp.models import Participant

# Create your models here.

def validate_return_date(value):
    """Valide que la date de retour est postérieure ou égale à la date d'emprunt"""
    # Cette validation sera complétée dans le clean() du modèle Borrowing
    pass

class Book(models.Model):
    id = models.AutoField(primary_key=True)
    title = models.CharField(max_length=200)
    author = models.CharField(max_length=200)
    publication_date = models.DateField()
    book_file = models.FileField(upload_to='books/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    # Relation avec Category
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='books')

    def __str__(self):
        return self.title

    def is_available(self):
        """Vérifie si le livre est disponible (pas d'emprunt en cours)"""
        return not self.borrowings.filter(returned=False).exists()

    def get_current_borrowing(self):
        """Retourne l'emprunt en cours s'il existe"""
        return self.borrowings.filter(returned=False).first()

    class Meta:
        verbose_name = "Livre"
        verbose_name_plural = "Livres"
        ordering = ['-publication_date']


class Borrowing(models.Model):
    borrowing_date = models.DateField(auto_now_add=True)
    return_date = models.DateField()
    returned = models.BooleanField(default=False)

    # Relations
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='borrowings')
    client = models.ForeignKey(Client, on_delete=models.CASCADE, related_name='borrowings')

    def clean(self):
        """Validation personnalisée"""
        super().clean()
        if self.return_date and self.borrowing_date:
            if self.return_date < self.borrowing_date:
                raise ValidationError('La date de retour doit être postérieure ou égale à la date d\'emprunt.')

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def __str__(self):
        status = "Retourné" if self.returned else "En cours"
        return f"{self.book.title} - {self.client.first_name} {self.client.last_name} ({status})"

    class Meta:
        verbose_name = "Emprunt"
        verbose_name_plural = "Emprunts"
        ordering = ['-borrowing_date']
